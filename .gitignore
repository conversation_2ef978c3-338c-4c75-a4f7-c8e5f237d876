# Node.js dependencies
node_modules

# Next.js build output
.next
out

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Prisma generated & database files
/prisma/dev.db
/prisma/dev.db-journal

# MacOS system files
.DS_Store

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# VSCode settings
.vscode/

# Editor backups
*.swp
*.swo

# Optional: log directory
logs
*.log
